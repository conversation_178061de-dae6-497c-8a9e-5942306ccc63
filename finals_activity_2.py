"""
Finals Activity 2 - Vigenère Cipher Program (Alternative Flow)
Instructions: Create a program that performs Vigenère cipher encryption and decryption.

This program follows a different flow:
1. Ask the user to enter a message
2. Ask the user to enter a key (to be used for encryption or decryption)
3. Ask the user whether they want to encrypt or decrypt the message
4. Process the message
5. Ask the user if they want to save the result to a text file
6. If the user answers yes, ask if they want to start again
7. If the user answers no, ask if they want to start again
8. If the user chooses to start again, the program should continue
9. If the user chooses not to start again, the program should end
"""

def vigenere_encrypt(plaintext, keyword):
    """
    Encrypts plaintext using Vigenère cipher with the given keyword.
    
    Args:
        plaintext (str): The message to encrypt
        keyword (str): The keyword for encryption
    
    Returns:
        str: The encrypted message
    """
    ciphertext = []
    keyword = keyword.upper()
    repeated_keyword = ""
    
    # Create repeated keyword that matches the length of alphabetic characters
    index_keyword = 0
    for char in plaintext:
        if char.isalpha():
            repeated_keyword += keyword[index_keyword % len(keyword)]
            index_keyword += 1
        else:
            repeated_keyword += char
    
    # Perform encryption
    for p_char, k_char in zip(plaintext, repeated_keyword):
        if p_char.isalpha():
            text_base = ord('A') if p_char.isupper() else ord('a')
            key_base = ord('A')
            shift = ((ord(p_char) - text_base) + (ord(k_char) - key_base)) % 26
            ciphertext.append(chr(text_base + shift))
        else:
            ciphertext.append(p_char)
    
    return ''.join(ciphertext)


def vigenere_decrypt(ciphertext, keyword):
    """
    Decrypts ciphertext using Vigenère cipher with the given keyword.
    
    Args:
        ciphertext (str): The message to decrypt
        keyword (str): The keyword for decryption
    
    Returns:
        str: The decrypted message
    """
    plaintext = []
    keyword = keyword.upper()
    repeated_keyword = ""
    
    # Create repeated keyword that matches the length of alphabetic characters
    k_index = 0
    for char in ciphertext:
        if char.isalpha():
            repeated_keyword += keyword[k_index % len(keyword)]
            k_index += 1
        else:
            repeated_keyword += char
    
    # Perform decryption
    for c_char, k_char in zip(ciphertext, repeated_keyword):
        if c_char.isalpha():
            text_base = ord('A') if c_char.isupper() else ord('a')
            key_base = ord('A')
            shift = ((ord(c_char) - text_base) - (ord(k_char) - key_base)) % 26
            plaintext.append(chr(text_base + shift))
        else:
            plaintext.append(c_char)
    
    return ''.join(plaintext)


def save_to_file(content, operation_type):
    """
    Saves the result to a text file.
    
    Args:
        content (str): The content to save
        operation_type (str): Either 'encrypted' or 'decrypted'
    """
    try:
        filename = f"activity2_{operation_type}_result.txt"
        with open(filename, 'w') as file:
            file.write(content)
        print(f"Result saved to {filename}")
        return True
    except Exception as e:
        print(f"Error saving file: {e}")
        return False


def get_valid_input(prompt, validation_func=None, error_message="Invalid input. Please try again."):
    """
    Gets valid input from user with optional validation.
    
    Args:
        prompt (str): The input prompt
        validation_func (function): Optional validation function
        error_message (str): Error message for invalid input
    
    Returns:
        str: Valid user input
    """
    while True:
        user_input = input(prompt).strip()
        if not user_input:
            print("Input cannot be empty. Please try again.")
            continue
        if validation_func is None or validation_func(user_input):
            return user_input
        print(error_message)


def main():
    """
    Main program function that handles the complete workflow.
    """
    print("=" * 60)
    print("Welcome to the Vigenère Cipher Program - Activity 2!")
    print("=" * 60)
    
    while True:
        try:
            # Step 1: Ask the user to enter a message
            message = get_valid_input(
                "\nStep 1: Enter a message: "
            )
            
            # Step 2: Ask the user to enter a key
            keyword = get_valid_input(
                "Step 2: Enter a key (to be used for encryption or decryption): ",
                lambda x: x.isalpha(),
                "Key must contain only alphabetic characters."
            )
            
            # Step 3: Ask the user whether they want to encrypt or decrypt the message
            print("\nStep 3: Choose operation:")
            print("1. Encrypt the message")
            print("2. Decrypt the message")
            
            choice = get_valid_input(
                "Enter your choice (1 or 2): ",
                lambda x: x in ['1', '2'],
                "Please enter either 1 or 2."
            )
            
            operation = "encrypt" if choice == '1' else "decrypt"
            operation_past = "encrypted" if choice == '1' else "decrypted"
            
            # Step 4: Process the message
            print(f"\nStep 4: Processing your message...")
            
            if operation == "encrypt":
                result = vigenere_encrypt(message, keyword)
            else:
                result = vigenere_decrypt(message, keyword)
            
            # Display the result
            print(f"\nOriginal message: {message}")
            print(f"Key: {keyword}")
            print(f"Result ({operation_past}): {result}")
            print("-" * 50)
            
            # Step 5: Ask the user if they want to save the result to a text file
            save_choice = get_valid_input(
                "\nStep 5: Would you like to save the result to a text file? (y/n): ",
                lambda x: x.lower() in ['y', 'yes', 'n', 'no'],
                "Please enter 'y' for yes or 'n' for no."
            ).lower()
            
            file_saved = False
            if save_choice in ['y', 'yes']:
                file_saved = save_to_file(result, operation_past)
            
            # Steps 6-9: Handle restart logic based on file save choice
            if file_saved:
                # Step 6: If user saved file, ask if they want to start again
                restart_choice = get_valid_input(
                    "\nStep 6: File saved! Would you like to start again? (y/n): ",
                    lambda x: x.lower() in ['y', 'yes', 'n', 'no'],
                    "Please enter 'y' for yes or 'n' for no."
                ).lower()
            else:
                # Step 7: If user didn't save file, ask if they want to start again
                restart_choice = get_valid_input(
                    "\nStep 7: Would you like to start again? (y/n): ",
                    lambda x: x.lower() in ['y', 'yes', 'n', 'no'],
                    "Please enter 'y' for yes or 'n' for no."
                ).lower()
            
            # Steps 8-9: Continue or end based on user choice
            if restart_choice in ['y', 'yes']:
                # Step 8: User chooses to start again, program continues
                print("\nStep 8: Starting again...\n")
                continue
            else:
                # Step 9: User chooses not to start again, program ends
                print("\nStep 9: Thank you for using the Vigenère Cipher Program!")
                print("Goodbye!")
                break
                
        except KeyboardInterrupt:
            print("\n\nProgram interrupted by user. Goodbye!")
            break
        except Exception as e:
            print(f"\nAn error occurred: {e}")
            print("Please try again.")


if __name__ == "__main__":
    main()
