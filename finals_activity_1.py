"""
Finals Activity 1 - Vigenère Cipher Program
Instructions: Create a program that performs Vigenère cipher encryption and decryption.

This program should:
1. Ask the user whether they want to encrypt or decrypt a message
2. Prompt the user to enter the message they want to process
3. Ask for a keyword to use in the encryption/decryption process
4. Process the message using the Vigenère cipher based on the chosen operation
5. Display the final result (either the encrypted or decrypted message)

Additional requirements:
- Ask the user to enter a message (to be used for encryption or decryption)
- Ask the user whether they want to encrypt or decrypt the message
- Process the message
- Ask the user if they want to save the result to a text file
- If the user answers yes, ask if they want to start again
- If the user chooses to start again, the program should continue
- If the user chooses not to start again, the program should end
"""

def vigenere_encrypt(plaintext, keyword):
    """
    Encrypts plaintext using Vigenère cipher with the given keyword.
    
    Args:
        plaintext (str): The message to encrypt
        keyword (str): The keyword for encryption
    
    Returns:
        str: The encrypted message
    """
    ciphertext = []
    keyword = keyword.upper()
    repeated_keyword = ""
    
    # Create repeated keyword that matches the length of alphabetic characters
    index_keyword = 0
    for char in plaintext:
        if char.isalpha():
            repeated_keyword += keyword[index_keyword % len(keyword)]
            index_keyword += 1
        else:
            repeated_keyword += char
    
    # Perform encryption
    for p_char, k_char in zip(plaintext, repeated_keyword):
        if p_char.isalpha():
            text_base = ord('A') if p_char.isupper() else ord('a')
            key_base = ord('A')
            shift = ((ord(p_char) - text_base) + (ord(k_char) - key_base)) % 26
            ciphertext.append(chr(text_base + shift))
        else:
            ciphertext.append(p_char)
    
    return ''.join(ciphertext)


def vigenere_decrypt(ciphertext, keyword):
    """
    Decrypts ciphertext using Vigenère cipher with the given keyword.
    
    Args:
        ciphertext (str): The message to decrypt
        keyword (str): The keyword for decryption
    
    Returns:
        str: The decrypted message
    """
    plaintext = []
    keyword = keyword.upper()
    repeated_keyword = ""
    
    # Create repeated keyword that matches the length of alphabetic characters
    k_index = 0
    for char in ciphertext:
        if char.isalpha():
            repeated_keyword += keyword[k_index % len(keyword)]
            k_index += 1
        else:
            repeated_keyword += char
    
    # Perform decryption
    for c_char, k_char in zip(ciphertext, repeated_keyword):
        if c_char.isalpha():
            text_base = ord('A') if c_char.isupper() else ord('a')
            key_base = ord('A')
            shift = ((ord(c_char) - text_base) - (ord(k_char) - key_base)) % 26
            plaintext.append(chr(text_base + shift))
        else:
            plaintext.append(c_char)
    
    return ''.join(plaintext)


def save_to_file(content, operation_type):
    """
    Saves the result to a text file.
    
    Args:
        content (str): The content to save
        operation_type (str): Either 'encrypted' or 'decrypted'
    """
    try:
        filename = f"vigenere_{operation_type}_result.txt"
        with open(filename, 'w') as file:
            file.write(content)
        print(f"Result saved to {filename}")
    except Exception as e:
        print(f"Error saving file: {e}")


def get_valid_input(prompt, validation_func=None, error_message="Invalid input. Please try again."):
    """
    Gets valid input from user with optional validation.
    
    Args:
        prompt (str): The input prompt
        validation_func (function): Optional validation function
        error_message (str): Error message for invalid input
    
    Returns:
        str: Valid user input
    """
    while True:
        user_input = input(prompt).strip()
        if not user_input:
            print("Input cannot be empty. Please try again.")
            continue
        if validation_func is None or validation_func(user_input):
            return user_input
        print(error_message)


def main():
    """
    Main program function that handles the complete workflow.
    """
    print("=" * 60)
    print("Welcome to the Vigenère Cipher Program!")
    print("=" * 60)
    
    while True:
        try:
            # Step 1: Ask user whether they want to encrypt or decrypt
            print("\nWhat would you like to do?")
            print("1. Encrypt a message")
            print("2. Decrypt a message")
            
            choice = get_valid_input(
                "Enter your choice (1 or 2): ",
                lambda x: x in ['1', '2'],
                "Please enter either 1 or 2."
            )
            
            operation = "encrypt" if choice == '1' else "decrypt"
            operation_past = "encrypted" if choice == '1' else "decrypted"
            
            # Step 2: Get the message to process
            message = get_valid_input(
                f"Enter the message to {operation}: "
            )
            
            # Step 3: Get the keyword
            keyword = get_valid_input(
                "Enter the keyword: ",
                lambda x: x.isalpha(),
                "Keyword must contain only alphabetic characters."
            )
            
            # Step 4: Process the message
            print(f"\nProcessing your message...")

            if operation == "encrypt":
                result = vigenere_encrypt(message, keyword)
            else:
                result = vigenere_decrypt(message, keyword)

            # Step 5: Display the result
            print(f"\nOriginal message: {message}")
            print(f"Keyword: {keyword}")
            print(f"Result ({operation_past}): {result}")
            print("-" * 50)
            
            # Ask if user wants to save to file
            save_choice = get_valid_input(
                "\nWould you like to save the result to a text file? (y/n): ",
                lambda x: x.lower() in ['y', 'yes', 'n', 'no'],
                "Please enter 'y' for yes or 'n' for no."
            ).lower()
            
            if save_choice in ['y', 'yes']:
                save_to_file(result, operation_past)
            
            # Ask if user wants to start again
            again_choice = get_valid_input(
                "\nWould you like to start again? (y/n): ",
                lambda x: x.lower() in ['y', 'yes', 'n', 'no'],
                "Please enter 'y' for yes or 'n' for no."
            ).lower()
            
            if again_choice not in ['y', 'yes']:
                print("\nThank you for using the Vigenère Cipher Program!")
                print("Goodbye!")
                break
                
        except KeyboardInterrupt:
            print("\n\nProgram interrupted by user. Goodbye!")
            break
        except Exception as e:
            print(f"\nAn error occurred: {e}")
            print("Please try again.")


if __name__ == "__main__":
    main()
