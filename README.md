# Finals Activities - Vigenère Cipher Programs

## Overview
This repository contains two different Vigenère cipher programs, each following a different workflow as specified in the activity requirements. Both programs implement complete Vigenère cipher systems that can encrypt and decrypt messages using keyword-based polyalphabetic substitution cipher.

## Features
- **Encryption**: Convert plaintext messages to encrypted ciphertext
- **Decryption**: Convert encrypted messages back to original plaintext
- **Interactive Interface**: User-friendly menu system
- **File Output**: Option to save results to text files
- **Input Validation**: Robust error handling and input validation
- **Case Preservation**: Maintains original case of letters
- **Non-alphabetic Characters**: Preserves spaces, numbers, and special characters

## Programs Included

### Finals Activity 1 (`finals_activity_1.py`)
**Workflow:**
1. Ask user whether they want to encrypt or decrypt a message
2. Prompt user to enter the message they want to process
3. Ask for a keyword to use in the encryption/decryption process
4. Process the message using the Vigenère cipher based on chosen operation
5. Display the final result (either encrypted or decrypted message)
6. Ask if user wants to save result to text file
7. Ask if user wants to start again

### Finals Activity 2 (`finals_activity_2.py`)
**Workflow:**
1. Ask the user to enter a message
2. Ask the user to enter a key (to be used for encryption or decryption)
3. Ask the user whether they want to encrypt or decrypt the message
4. Process the message
5. Ask the user if they want to save the result to a text file
6. If user saves file, ask if they want to start again
7. If user doesn't save file, ask if they want to start again
8. If user chooses to start again, program continues
9. If user chooses not to start again, program ends

## Files Included
- `finals_activity_1.py` - Activity 1 program file
- `finals_activity_2.py` - Activity 2 program file
- `test_vigenere.py` - Test script to verify functionality
- `README.md` - This documentation file
- `Vigenere Cipher Encryption Samplecode.pdf` - Reference encryption code
- `Vigenere Cipher Decryption Samplecode.pdf` - Reference decryption code

## How to Run

### For Activity 1:
1. Make sure you have Python installed on your system
2. Open a terminal/command prompt
3. Navigate to the program directory
4. Run the program:
   ```
   python finals_activity_1.py
   ```

### For Activity 2:
1. Make sure you have Python installed on your system
2. Open a terminal/command prompt
3. Navigate to the program directory
4. Run the program:
   ```
   python finals_activity_2.py
   ```

## Program Flow
The program follows these steps as specified in the requirements:

1. **Welcome Message**: Displays program title and options
2. **Operation Selection**: Ask user to choose encryption or decryption
3. **Message Input**: Prompt user to enter the message to process
4. **Keyword Input**: Ask for the encryption/decryption keyword
5. **Processing**: Apply Vigenère cipher algorithm
6. **Display Results**: Show original message, keyword, and result
7. **File Save Option**: Ask if user wants to save result to file
8. **Continue Option**: Ask if user wants to start again
9. **Exit**: Thank user and end program

## Usage Examples

### Encryption Example
```
What would you like to do?
1. Encrypt a message
2. Decrypt a message
Enter your choice (1 or 2): 1
Enter the message to encrypt: Hello World
Enter the keyword: KEY

Processing your message...

Original message: Hello World
Keyword: KEY
Result (encrypted): Rijvs Uyvjn
```

### Decryption Example
```
What would you like to do?
1. Encrypt a message
2. Decrypt a message
Enter your choice (1 or 2): 2
Enter the message to decrypt: Rijvs Uyvjn
Enter the keyword: KEY

Processing your message...

Original message: Rijvs Uyvjn
Keyword: KEY
Result (decrypted): Hello World
```

## How the Vigenère Cipher Works

The Vigenère cipher uses a keyword to encrypt/decrypt messages:

1. **Keyword Repetition**: The keyword is repeated to match the length of alphabetic characters in the message
2. **Character Shifting**: Each letter is shifted by the corresponding keyword letter's position in the alphabet
3. **Case Preservation**: Uppercase and lowercase letters are handled separately
4. **Non-alphabetic Preservation**: Spaces, numbers, and special characters remain unchanged

### Example:
- Message: "HELLO"
- Keyword: "KEY"
- Repeated Keyword: "KEYKE"
- Encryption: H+K=R, E+E=I, L+Y=J, L+K=V, O+E=S
- Result: "RIJVS"

## Testing
Run the test script to verify the program works correctly:
```
python test_vigenere.py
```

This will run various test cases to ensure both encryption and decryption functions work properly.

## Requirements Met
✅ Ask user whether they want to encrypt or decrypt a message  
✅ Prompt user to enter the message they want to process  
✅ Ask for a keyword to use in the encryption/decryption process  
✅ Process the message using the Vigenère cipher based on chosen operation  
✅ Display the final result (either encrypted or decrypted message)  
✅ Ask user if they want to save the result to a text file  
✅ Ask if user wants to start again  
✅ Continue or end program based on user choice  

## Error Handling
- Empty input validation
- Keyword must contain only alphabetic characters
- Graceful handling of unexpected errors
- Keyboard interrupt (Ctrl+C) handling

## Author
Created for Finals Activity 1 - System Development and Analysis Laboratory
