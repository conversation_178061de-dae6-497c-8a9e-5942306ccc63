"""
Test script for the Vigenère Cipher Program
This script tests the encryption and decryption functions with various test cases.
"""

from finals_activity_1 import vigenere_encrypt, vigenere_decrypt

def test_vigenere_cipher():
    """Test the Vigenère cipher with various test cases."""
    
    print("Testing Vigenère Cipher Functions")
    print("=" * 50)
    
    # Test cases: (plaintext, keyword, expected_ciphertext)
    test_cases = [
        ("HELLO", "KEY", "RIJVS"),
        ("Hello World", "KEY", "Rijvs Uyvjn"),
        ("ATTACKATDAWN", "LEMON", "LXFOPVEFRNHR"),
        ("The quick brown fox", "SECRET", "Llg jcmow dvsiz hsx"),
        ("Programming is fun!", "CODE", "Rvskvcoqmrk ku hwr!"),
        ("123 ABC 456", "TEST", "123 TUU 456"),  # Test with numbers and spaces
    ]
    
    print("Encryption Tests:")
    print("-" * 30)
    
    for i, (plaintext, keyword, expected) in enumerate(test_cases, 1):
        encrypted = vigenere_encrypt(plaintext, keyword)
        status = "✓ PASS" if encrypted == expected else "✗ FAIL"
        
        print(f"Test {i}: {status}")
        print(f"  Plaintext:  '{plaintext}'")
        print(f"  Keyword:    '{keyword}'")
        print(f"  Expected:   '{expected}'")
        print(f"  Got:        '{encrypted}'")
        print()
    
    print("Decryption Tests:")
    print("-" * 30)
    
    for i, (original_plaintext, keyword, ciphertext) in enumerate(test_cases, 1):
        decrypted = vigenere_decrypt(ciphertext, keyword)
        status = "✓ PASS" if decrypted == original_plaintext else "✗ FAIL"
        
        print(f"Test {i}: {status}")
        print(f"  Ciphertext: '{ciphertext}'")
        print(f"  Keyword:    '{keyword}'")
        print(f"  Expected:   '{original_plaintext}'")
        print(f"  Got:        '{decrypted}'")
        print()
    
    print("Round-trip Tests (Encrypt then Decrypt):")
    print("-" * 45)
    
    round_trip_tests = [
        ("This is a test message", "PYTHON"),
        ("Vigenère cipher is secure!", "CRYPTOGRAPHY"),
        ("Mixed Case and Numbers 123", "ALGORITHM"),
        ("Special chars: !@#$%^&*()", "SECURITY"),
    ]
    
    for i, (message, keyword) in enumerate(round_trip_tests, 1):
        encrypted = vigenere_encrypt(message, keyword)
        decrypted = vigenere_decrypt(encrypted, keyword)
        status = "✓ PASS" if decrypted == message else "✗ FAIL"
        
        print(f"Test {i}: {status}")
        print(f"  Original:   '{message}'")
        print(f"  Keyword:    '{keyword}'")
        print(f"  Encrypted:  '{encrypted}'")
        print(f"  Decrypted:  '{decrypted}'")
        print()

if __name__ == "__main__":
    test_vigenere_cipher()
